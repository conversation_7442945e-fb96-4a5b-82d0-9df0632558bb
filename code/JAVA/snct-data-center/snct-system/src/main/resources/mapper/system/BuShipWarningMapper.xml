<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuShipWarningMapper">
    
    <resultMap type="BuShipWarning" id="BuShipWarningResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="sn"    column="sn"    />
        <result property="deviceId"    column="device_id"    />
        <result property="dataKey"    column="data_key"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="level"    column="level"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deptName"    column="dept_name"    />
        <result property="shipName"    column="ship_name"    />
        <result property="deviceName"    column="device_name"    />
    </resultMap>

    <sql id="selectBuShipWarningVo">
        select w.id, w.dept_id, w.sn, w.device_id, w.data_key, w.name, w.type, w.level, w.status, w.remark,
               w.create_by, w.create_time,w.update_by, w.update_time,d.dept_name,ss.name as ship_name,de.name as device_name
        from bu_ship_warning w
        INNER join sys_dept d on w.dept_id = d.dept_id
        INNER join bu_ship ss on w.sn = ss.sn
        left join bu_device de on w.device_id = de.id
    </sql>

    <select id="selectBuShipWarningList" parameterType="BuShipWarning" resultMap="BuShipWarningResult">
        <include refid="selectBuShipWarningVo"/>
        <where>
            <if test="deptId != null "> and w.dept_id = #{deptId}</if>
            <if test="sn != null  and sn != ''"> and w.sn = #{sn}</if>
            <if test="deviceId != null "> and w.device_id = #{deviceId}</if>
            <if test="dataKey != null  and dataKey != ''"> and w.data_key = #{dataKey}</if>
            <if test="name != null  and name != ''"> and w.name like concat('%', #{name}, '%')</if>
            <if test="deptName != null  and deptName != ''"> and d.dept_name like concat('%', #{deptName}, '%')</if>
            <if test="shipName != null  and shipName != ''"> and ss.name like concat('%', #{shipName}, '%')</if>
            <if test="deviceName != null  and deviceName != ''"> and de.name like concat('%', #{deviceName}, '%')</if>
            <if test="type != null "> and w.type = #{type}</if>
            <if test="level != null "> and w.level = #{level}</if>
            <if test="status != null "> and w.status = #{status}</if>
            <if test="stime != null and stime != ''"><!-- 开始时间检索 -->
                AND w.create_time &gt;= #{stime}
            </if>
            <if test="etime != null and etime != ''"><!-- 结束时间检索 -->
                AND w.create_time &lt;= #{etime}
            </if>
        </where>
        ${params.dataScope}
        order by w.create_time desc
    </select>
    
    <select id="selectBuShipWarningById" parameterType="Long" resultMap="BuShipWarningResult">
        <include refid="selectBuShipWarningVo"/>
        where w.id = #{id}
    </select>

    <insert id="insertBuShipWarning" parameterType="BuShipWarning" useGeneratedKeys="true" keyProperty="id">
        insert into bu_ship_warning
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="sn != null and sn != ''">sn,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="dataKey != null">data_key,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="type != null">type,</if>
            <if test="level != null">level,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="sn != null and sn != ''">#{sn},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="dataKey != null">#{dataKey},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="level != null">#{level},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBuShipWarning" parameterType="BuShipWarning">
        update bu_ship_warning
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="sn != null and sn != ''">sn = #{sn},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="dataKey != null">data_key = #{dataKey},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="level != null">level = #{level},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuShipWarningById" parameterType="Long">
        delete from bu_ship_warning where id = #{id}
    </delete>

    <delete id="deleteBuShipWarningByIds" parameterType="String">
        delete from bu_ship_warning where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>